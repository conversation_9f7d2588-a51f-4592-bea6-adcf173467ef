"use client";

import { CodeforcesRatingGraph } from "@/components/codeforces/CodeforcesRatingGraph";
import { TwoPartModal } from "@/components/ui/reusable-modal";
import { useAuth } from "@/lib/auth-context";
import { signInWithPopup } from "@/lib/auth-popup";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { FileText, Link2, Loader2, LogIn } from "lucide-react";
import { useEffect, useState } from "react";

const CodeforcesTestPage = () => {
  const [handle, setHandle] = useState("");
  const [graphHandle, setGraphHandle] = useState("");
  const [showFloatingButton, setShowFloatingButton] = useState(false);
  const [isVerifyModalOpen, setIsVerifyModalOpen] = useState(false);
  const [isAuthenticating, setIsAuthenticating] = useState(false);

  // Auth context
  const { user, loading: authLoading } = useAuth();
  const queryClient = useQueryClient();

  // Check if Codeforces handle is verified
  const { data: isCodeforcesVerified, isLoading: isVerificationLoading } =
    useQuery({
      queryKey: ["isVerified", user?.email],
      queryFn: async () => {
        if (!user?.email) {
          throw new Error("User email not found");
        }
        const response = await fetch(`/api/codeforces/verifyHandle`);
        if (!response.ok) {
          return false;
        }
        return true;
      },
      enabled: !!user?.email,
      staleTime: 0,
      gcTime: 5 * 60 * 1000,
      refetchOnMount: true,
      refetchOnWindowFocus: false,
      retry: 1,
      retryDelay: 1000,
    });

  // Scroll detection for floating button
  useEffect(() => {
    const handleScroll = () => {
      const scrollY = window.scrollY;
      const windowHeight = window.innerHeight;
      // Show floating button when user scrolls down more than 50% of viewport height
      setShowFloatingButton(scrollY > windowHeight * 0.5);
    };

    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  const handleShowGraph = () => {
    if (handle.trim()) {
      setGraphHandle(handle);
    }
  };

  const scrollToCreateSheet = () => {
    // Scroll to the bottom of the page where create sheet functionality is
    window.scrollTo({
      top: document.documentElement.scrollHeight,
      behavior: "smooth",
    });
  };

  // Handle login with popup
  const handleLogin = async () => {
    if (isAuthenticating) return;

    setIsAuthenticating(true);
    try {
      const result = await signInWithPopup("google", {
        width: 500,
        height: 600,
        timeout: 300000,
      });

      if (result.success) {
        // Invalidate queries to refresh user data
        queryClient.invalidateQueries({ queryKey: ["userInfo"] });
        queryClient.invalidateQueries({ queryKey: ["isVerified"] });
      }
    } catch (error) {
      console.error("Login failed:", error);
    } finally {
      setIsAuthenticating(false);
    }
  };

  // Handle Codeforces verification
  const handleVerificationSuccess = () => {
    queryClient.invalidateQueries({ queryKey: ["isVerified"] });
    setIsVerifyModalOpen(false);
  };

  const handleVerificationFailed = () => {
    setIsVerifyModalOpen(false);
  };

  return (
    <div className="min-h-screen relative pt-16">
      {/* Background gradient like home page */}
      <div className="absolute -top-20 left-1/2 -translate-x-1/2 h-[400px] w-[400px] bg-gradient-to-br from-blue-900 via-blue-800 to-cyan-700 opacity-25 blur-3xl rounded-full -z-10"></div>

      <div className="container mx-auto p-6 max-w-[95%] mt-16">
        {/* Clean Professional Heading */}
        <div className="text-center mb-12">
          <h1 className="text-6xl md:text-5xl font-light text-white mb-3 tracking-wide ">
            SheetScope
          </h1>
          <p className="text-gray-400 text-lg font-light">
            From timeline to training
          </p>
        </div>

        {/* Codeforces Handle Input Section */}
        <div className="mb-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 items-end">
            <div>
              <label
                htmlFor="handle"
                className="block text-lg font-medium mb-2"
              >
                Codeforces Handle
              </label>
              <input
                id="handle"
                type="text"
                value={handle}
                onChange={(e) => setHandle(e.target.value)}
                placeholder="Enter Codeforces username (e.g., tourist)"
                className="w-full px-3 py-2 bg-input border border-border rounded-md focus:outline-none focus:ring-2 focus:ring-ring text-foreground placeholder:text-muted-foreground"
                onKeyDown={(e) => e.key === "Enter" && handleShowGraph()}
                suppressHydrationWarning={true}
              />
            </div>
            <div className="flex gap-2">
              <button
                onClick={handleShowGraph}
                disabled={!handle.trim()}
                className="w-full px-6 py-2 bg-gradient-to-r from-blue-600 to-cyan-600 text-white rounded-md hover:from-blue-700 hover:to-cyan-700 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300 hover:shadow-lg hover:shadow-blue-500/25 cursor-pointer"
              >
                🔍 Search
              </button>
            </div>
          </div>
        </div>

        {/* Professional Feature Explanation Section - Hidden when graph is shown */}
        {!graphHandle.trim() && (
          <div className="mb-12 relative group">
            {/* Enhanced glassy background with multiple layers */}
            <div className="absolute inset-0 bg-gradient-to-br from-blue-900/20 via-slate-800/30 to-cyan-900/20 rounded-2xl blur-2xl"></div>
            <div className="absolute inset-0 bg-gradient-to-tr from-slate-900/40 via-transparent to-slate-800/40 rounded-2xl blur-lg"></div>

            {/* Subtle animated border glow */}
            <div className="absolute inset-0 bg-gradient-to-r from-blue-500/20 via-cyan-500/20 to-blue-500/20 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-1000 blur-sm"></div>

            <div className="relative bg-gradient-to-br from-slate-900/60 via-slate-800/40 to-slate-900/60 backdrop-blur-lg border border-slate-600/30 rounded-2xl p-8 shadow-2xl shadow-blue-500/10 hover:shadow-blue-500/15 transition-all duration-700 hover:border-slate-500/40">
              {/* Header Section */}
              <div className="text-center mb-10">
                <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-blue-600/80 via-cyan-600/60 to-blue-700/80 backdrop-blur-sm rounded-2xl mb-6 shadow-xl shadow-blue-500/20 border border-blue-400/30">
                  <FileText />
                </div>
                <h2 className="text-3xl font-bold mb-4 text-white font-poppins">
                  Create Custom Sheets
                </h2>
                <p className="text-slate-300 text-lg max-w-3xl mx-auto leading-relaxed font-inter">
                  Turn any Codeforces profile into a personalized problem sheet.
                </p>
              </div>

              {/* Enhanced Steps Grid */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-10">
                {/* Step 1 - Enhanced */}
                <div className="group relative">
                  <div className="absolute inset-0 bg-gradient-to-br from-blue-600/10 to-slate-700/10 rounded-xl blur-lg group-hover:blur-xl transition-all duration-500"></div>
                  <div className="relative bg-gradient-to-br from-slate-800/60 to-slate-900/60 backdrop-blur-sm border border-slate-600/30 rounded-xl p-6 hover:border-blue-400/40 transition-all duration-500 hover:transform hover:scale-105 hover:shadow-xl hover:shadow-blue-500/20">
                    <div className="flex items-center mb-4">
                      <div className="relative">
                        <div className="w-10 h-10 bg-gradient-to-br from-slate-600 to-slate-700 rounded-xl flex items-center justify-center text-sm font-bold text-white shadow-lg">
                          01
                        </div>
                      </div>
                      <div className="ml-4">
                        <h3 className="font-semibold text-white text-base font-poppins">
                          Profile Discovery
                        </h3>
                        <div className="w-8 h-0.5 bg-gradient-to-r from-slate-400 to-transparent mt-1"></div>
                      </div>
                    </div>
                    <p className="text-slate-300 text-sm leading-relaxed font-inter">
                      Enter a Codeforces username to fetch their Profile. This
                      will enable you to see their interactive rating graph.
                    </p>
                  </div>
                </div>

                {/* Step 2 - Enhanced */}
                <div className="group relative">
                  <div className="absolute inset-0 bg-gradient-to-br from-emerald-600/10 to-emerald-700/10 rounded-xl blur-lg group-hover:blur-xl transition-all duration-500"></div>
                  <div className="relative bg-gradient-to-br from-slate-800/60 to-slate-900/60 backdrop-blur-sm border border-emerald-500/30 rounded-xl p-6 hover:border-emerald-400/40 transition-all duration-500 hover:transform hover:scale-105 hover:shadow-xl hover:shadow-emerald-500/20">
                    <div className="flex items-center mb-4">
                      <div className="relative">
                        <div className="w-10 h-10 bg-gradient-to-br from-emerald-600 to-emerald-700 rounded-xl flex items-center justify-center text-sm font-bold text-white shadow-lg">
                          02
                        </div>
                      </div>
                      <div className="ml-4">
                        <h3 className="font-semibold text-white text-base font-poppins">
                          Interactive Analysis
                        </h3>
                        <div className="w-8 h-0.5 bg-gradient-to-r from-emerald-400 to-transparent mt-1"></div>
                      </div>
                    </div>
                    <p className="text-slate-300 text-sm leading-relaxed font-inter">
                      Explore rating graphs and performance patterns. Select
                      date ranges to focus on specific time periods.
                    </p>
                  </div>
                </div>

                {/* Step 3 - Enhanced */}
                <div className="group relative">
                  <div className="absolute inset-0 bg-gradient-to-br from-violet-600/10 to-violet-700/10 rounded-xl blur-lg group-hover:blur-xl transition-all duration-500"></div>
                  <div className="relative bg-gradient-to-br from-slate-800/60 to-slate-900/60 backdrop-blur-sm border border-violet-500/30 rounded-xl p-6 hover:border-violet-400/40 transition-all duration-500 hover:transform hover:scale-105 hover:shadow-xl hover:shadow-violet-500/20">
                    <div className="flex items-center mb-4">
                      <div className="relative">
                        <div className="w-10 h-10 bg-gradient-to-br from-violet-600 to-violet-700 rounded-xl flex items-center justify-center text-sm font-bold text-white shadow-lg">
                          03
                        </div>
                      </div>
                      <div className="ml-4">
                        <h3 className="font-semibold text-white text-base font-poppins">
                          Problem Filtering
                        </h3>
                        <div className="w-8 h-0.5 bg-gradient-to-r from-violet-400 to-transparent mt-1"></div>
                      </div>
                    </div>
                    <p className="text-slate-300 text-sm leading-relaxed font-inter">
                      Use advanced filters by rating, tags, and date ranges to
                      curate your ideal problem set.
                    </p>
                  </div>
                </div>

                {/* Step 4 - Enhanced with Sheet Creation Focus */}
                <div className="group relative">
                  <div className="absolute inset-0 bg-gradient-to-br from-blue-600/15 to-cyan-700/15 rounded-xl blur-lg group-hover:blur-xl transition-all duration-500"></div>
                  <div className="relative bg-gradient-to-br from-blue-800/60 to-cyan-900/60 backdrop-blur-sm border border-blue-500/40 rounded-xl p-6 hover:border-blue-400/60 transition-all duration-500 hover:transform hover:scale-105 hover:shadow-xl hover:shadow-blue-500/25">
                    <div className="flex items-center mb-4">
                      <div className="relative">
                        <div className="w-10 h-10 bg-gradient-to-br from-red-600 to-red-700 rounded-xl flex items-center justify-center text-sm font-bold text-white shadow-lg">
                          04
                        </div>
                      </div>
                      <div className="ml-4">
                        <h3 className="font-semibold text-white text-base font-poppins">
                          Problem Filtering
                        </h3>
                        <div className="w-8 h-0.5 bg-gradient-to-r from-violet-400 to-transparent mt-1"></div>
                      </div>
                    </div>
                    <p className="text-slate-300 text-sm leading-relaxed font-inter">
                      <strong className="text-blue-300">
                        Create personalized problem sheets
                      </strong>{" "}
                      powered by Codeforces sync, rating-based filtering, and
                      progress tracking.
                    </p>
                  </div>
                </div>
              </div>

              {/* Enhanced Key Features */}
              <div className="border-t border-slate-600/40 pt-8 backdrop-blur-sm">
                <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-6">
                  <div className="flex items-center group">
                    <div className="w-3 h-3 bg-gradient-to-br from-slate-400 to-slate-500 rounded-full mr-3 group-hover:scale-110 transition-transform duration-300 shadow-lg"></div>
                    <span className="text-slate-300 text-sm font-medium font-inter group-hover:text-white transition-colors duration-300">
                      Interactive Graphs
                    </span>
                  </div>
                  <div className="flex items-center group">
                    <div className="w-3 h-3 bg-gradient-to-br from-emerald-400 to-emerald-500 rounded-full mr-3 group-hover:scale-110 transition-transform duration-300 shadow-lg"></div>
                    <span className="text-slate-300 text-sm font-medium font-inter group-hover:text-white transition-colors duration-300">
                      Daily Analytics
                    </span>
                  </div>
                  <div className="flex items-center group">
                    <div className="w-3 h-3 bg-gradient-to-br from-violet-400 to-violet-500 rounded-full mr-3 group-hover:scale-110 transition-transform duration-300 shadow-lg"></div>
                    <span className="text-slate-300 text-sm font-medium font-inter group-hover:text-white transition-colors duration-300">
                      Range Selection
                    </span>
                  </div>
                  <div className="flex items-center group">
                    <div className="w-3 h-3 bg-gradient-to-br from-blue-400 to-cyan-500 rounded-full mr-3 group-hover:scale-110 transition-transform duration-300 shadow-lg"></div>
                    <span className="text-slate-300 text-sm font-medium font-inter group-hover:text-white transition-colors duration-300">
                      <strong>Sheet Creation</strong>
                    </span>
                  </div>
                  <div className="flex items-center group">
                    <div className="w-3 h-3 bg-gradient-to-br from-teal-400 to-teal-500 rounded-full mr-3 group-hover:scale-110 transition-transform duration-300 shadow-lg"></div>
                    <span className="text-slate-300 text-sm font-medium font-inter group-hover:text-white transition-colors duration-300">
                      Advanced Filters
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Rating Graph Section */}
        {graphHandle.trim() && (
          <div className="mb-8">
            <CodeforcesRatingGraph
              handle={graphHandle}
              height={450}
              width={1200}
            />
          </div>
        )}
      </div>

      {/* Smart Floating Button */}
      {showFloatingButton && (
        <div className="fixed bottom-6 right-6 z-50">
          {authLoading || isVerificationLoading ? (
            // Loading state
            <div className="group bg-slate-800/90 backdrop-blur-sm border border-slate-600/50 text-white p-4 rounded-2xl shadow-2xl">
              <div className="flex items-center gap-3">
                <Loader2 className="w-5 h-5 animate-spin text-slate-300" />
                <span className="hidden sm:block font-medium text-sm">
                  Loading...
                </span>
              </div>
            </div>
          ) : !user ? (
            // Not logged in - show login button
            <button
              onClick={handleLogin}
              disabled={isAuthenticating}
              className="group relative bg-gradient-to-r from-green-600 to-emerald-600 hover:from-green-700 hover:to-emerald-700 text-white p-4 rounded-2xl shadow-2xl hover:shadow-green-500/20 transition-all duration-300 transform hover:scale-105 disabled:opacity-50 disabled:cursor-not-allowed border border-green-500/20 hover:border-green-400/30"
              title="Login to Create Coding Sheets"
            >
              <div className="flex items-center gap-3">
                {isAuthenticating ? (
                  <Loader2 className="w-5 h-5 animate-spin" />
                ) : (
                  <LogIn className="w-5 h-5" />
                )}
                <span className="hidden sm:block font-medium text-sm">
                  {isAuthenticating ? "Logging in..." : "Login to Create"}
                </span>
              </div>
              <div className="absolute inset-0 bg-gradient-to-r from-green-600/20 to-emerald-600/20 rounded-2xl blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-500 -z-10"></div>
            </button>
          ) : !isCodeforcesVerified ? (
            // Logged in but Codeforces not connected
            <button
              onClick={() => setIsVerifyModalOpen(true)}
              className="group relative bg-gradient-to-r from-yellow-600 to-orange-600 hover:from-yellow-700 hover:to-orange-700 text-white p-4 rounded-2xl shadow-2xl hover:shadow-yellow-500/20 transition-all duration-300 transform hover:scale-105 border border-yellow-500/20 hover:border-yellow-400/30"
              title="Connect Codeforces to Create Sheets"
            >
              <div className="flex items-center gap-3">
                <Link2 className="w-5 h-5" />
                <span className="hidden sm:block font-medium text-sm">
                  Connect Codeforces
                </span>
              </div>
              <div className="absolute inset-0 bg-gradient-to-r from-yellow-600/20 to-orange-600/20 rounded-2xl blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-500 -z-10"></div>
            </button>
          ) : (
            // Ready to create sheet
            <button
              onClick={scrollToCreateSheet}
              className="group relative bg-gradient-to-r from-blue-600 to-cyan-600 hover:from-blue-700 hover:to-cyan-700 text-white p-4 rounded-2xl shadow-2xl hover:shadow-blue-500/20 transition-all duration-300 transform hover:scale-105 border border-blue-500/20 hover:border-blue-400/30"
              title="Create Your Coding Sheet"
            >
              <div className="flex items-center gap-3">
                <FileText className="w-5 h-5" />
                <span className="hidden sm:block font-medium text-sm">
                  Create Sheet
                </span>
              </div>
              <div className="absolute inset-0 bg-gradient-to-r from-blue-600/20 to-cyan-600/20 rounded-2xl blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-500 -z-10"></div>
            </button>
          )}
        </div>
      )}

      {/* Codeforces Verification Modal */}
      <TwoPartModal
        isOpen={isVerifyModalOpen}
        onClose={() => setIsVerifyModalOpen(false)}
        title="Connect Codeforces Account"
        description="Connect your Codeforces handle to create personalized coding sheets."
        onComplete={() => {}}
        onVerificationSuccess={handleVerificationSuccess}
        onVerificationFailed={handleVerificationFailed}
      />
    </div>
  );
};

export default CodeforcesTestPage;
